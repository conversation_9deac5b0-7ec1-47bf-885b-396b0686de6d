<html>
{% load static %}
<head>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.0/css/bootstrap.min.css">
<!-- jQuery library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
<!-- Latest compiled JavaScript -->
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.0/js/bootstrap.min.js"></script>

<style>
body {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8eaf6 100%);
    min-height: 100vh;
}

#navbar {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(26, 35, 126, 0.2);
}

.row1 {
    padding-top: 10px;
    padding-right: 50px
}

.row2 {
    padding-bottom: 20px
}

.navbar-input {
    padding: 11px 16px;
    border-radius: 2px 0 0 2px;
    border: 0 none;
    outline: 0 none;
    font-size: 15px
}

.navbar-button {
    background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
    border: 1px solid #00c853;
    border-radius: 0 6px 6px 0;
    color: #ffffff;
    padding: 10px 0;
    height: 43px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.navbar-button:hover {
    background: linear-gradient(135deg, #00e676 0%, #66bb6a 100%);
    transform: translateY(-1px);
}

.cart-button {
    background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);
    box-shadow: 0 4px 16px rgba(255, 112, 67, 0.3);
    padding: 10px 0;
    text-align: center;
    height: 41px;
    border-radius: 8px;
    font-weight: 600;
    width: 120px;
    display: inline-block;
    color: #FFFFFF;
    text-decoration: none;
    border: none;
    outline: none;
    transition: all 0.3s ease;
}

.cart-button:hover {
    text-decoration: none;
    color: #fff;
    cursor: pointer;
    background: linear-gradient(135deg, #ff8a65 0%, #ffab91 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 112, 67, 0.4);
}

.cart-svg {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 8px
}

.item-number {
    border-radius: 3px;
    background-color: rgba(0, 0, 0, .1);
    height: 20px;
    padding: 3px 6px;
    font-weight: 500;
    display: inline-block;
    color: #fff;
    line-height: 12px;
    margin-left: 10px
}

.upper-links {
    display: inline-block;
    padding: 0 11px;
    line-height: 23px;
    font-family: 'Roboto', sans-serif;
    letter-spacing: 0;
    color: inherit;
    border: none;
    outline: none;
    font-size: 12px
}

.dropdown {
    position: relative;
    display: inline-block;
    margin-bottom: 0px
}

.dropdown:hover {
    background-color: #fff
}

.dropdown:hover .links {
    color: #000
}

.dropdown:hover .dropdown-menu {
    display: block
}

.dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    display: none;
    background-color: #fff;
    color: #333;
    left: 0px;
    border: 0;
    border-radius: 0;
    box-shadow: 0 4px 8px -3px #555454;
    margin: 0;
    padding: 0px
}

.links {
    color: #fff;
    text-decoration: none
}

.links:hover {
    color: #fff;
    text-decoration: none
}

.profile-links {
    font-size: 12px;
    font-family: 'Roboto', sans-serif;
    border-bottom: 1px solid #e9e9e9;
    box-sizing: border-box;
    display: block;
    padding: 0 11px;
    line-height: 23px
}

.profile-li {
    padding-top: 2px
}

.largenav {
    display: none
}

.smallnav {
    display: block
}

.smallsearch {
    margin-left: 15px;
    margin-top: 15px
}

.menu {
    cursor: pointer
}

        /*---------------------------------------
           Social section
        -----------------------------------------*/
        footer {
          padding: 0px 0px 0px 0px;
          background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
          margin: 0px;
          box-shadow: 0 -4px 16px rgba(26, 35, 126, 0.2);
        }


        #ftr {

          padding: 20px;
        }

        .fa {

          font-size: 23px;
          width: 60px;
          text-align: center;
          text-decoration: none;
          margin: 5px 2px;
          border-radius: 50%;
        }

        .fa:hover {
          opacity: 0.5;
          text-decoration: none;
        }

        .fa-facebook {
          background: #3B5998;
          color: white;
          margin-top: 30px;
        }

        .fa-whatsapp {
          background: #25d366;
          color: white;
        }

        .fa-twitter {
          background: #55ACEE;
          color: white;
        }

        .fa-instagram {
          background: #125688;
          color: white;
        }

        p {
          text-align: center;

        }


@media screen and (min-width: 768px) {
    .largenav {
        display: block
    }

    .smallnav {
        display: none
    }

    .smallsearch {
        margin: 0px
    }
}
</style>
</head>
<body>
<div id="navbar">
    <div class="container">
        <div class="row row1">
            <ul class="largenav pull-right">

                <li class="upper-links"><a class="links" href="/my-order">Orders</a></li>
                <li class="upper-links"><a class="links" href="/my-profile">Profile</a></li>
                  <li class="upper-links"><a class="links" href="/logout">Logout</a></li>
                <li class="upper-links dropdown"><a class="links" href="">More</a>
                    <ul class="dropdown-menu">
                      <li class="profile-li"><a class="profile-links" href="/send-feedback">Send Feedback</a></li>
                        <li class="profile-li"><a class="profile-links" href="/contactus">Contact Us</a></li>
                        <li class="profile-li"><a class="profile-links" href="/aboutus">About Us</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="row row2">
            <div class="col-sm-2">
                <h2 style="margin:0px;"><a style="text-decoration:none; color:white;" href="/"><span class="largenav">Online Shopping </span></a></h2>
            </div>

            <div class="navbar-search smallsearch col-sm-8 col-xs-11">
              <form  action="/search" method="get">
                <div class="row"> <input class="navbar-input col-xs-11" type="search" placeholder="Search for Products, Brands and more" name="query" id="query">
                  <button class="navbar-button col-xs-1" type="submit">
                    <svg width="15px" height="15px">
                            <path d="M11.618 9.897l4.224 4.212c.092.09.1.23.02.312l-1.464 1.46c-.08.08-.222.072-.314-.02L9.868 11.66M6.486 10.9c-2.42 0-4.38-1.955-4.38-4.367 0-2.413 1.96-4.37 4.38-4.37s4.38 1.957 4.38 4.37c0 2.412-1.96 4.368-4.38 4.368m0-10.834C2.904.066 0 2.96 0 6.533 0 10.105 2.904 13 6.486 13s6.487-2.895 6.487-6.467c0-3.572-2.905-6.467-6.487-6.467 "></path>
                    </svg>
                  </button>
                </div>
              </form>
            </div>


            <div class="cart1 largenav col-sm-2"> <a class="cart-button" href="/cart"> <svg class="cart-svg " width="16 " height="16 " viewBox="0 0 16 16 ">
                        <path d="M15.32 2.405H4.887C3 2.405 2.46.805 2.46.805L2.257.21C2.208.085 2.083 0 1.946 0H.336C.1 0-.064.24.024.46l.644 1.945L3.11 9.767c.047.137.175.23.32.23h8.418l-.493 1.958H3.768l.002.003c-.017 0-.033-.003-.05-.003-1.06 0-1.92.86-1.92 1.92s.86 1.92 1.92 1.92c.99 0 1.805-.75 1.91-1.712l5.55.076c.12.922.91 1.636 1.867 1.636 1.04 0 1.885-.844 1.885-1.885 0-.866-.584-1.593-1.38-1.814l2.423-8.832c.12-.433-.206-.86-.655-.86 " fill="#fff "></path>
                    </svg> Cart <span class="item-number ">{{ product_count_in_cart }}</span> </a> </div>
        </div>
    </div>
</div>
<!-- content start-->
{% block content %}

{% endblock content %}
<!-- content end-->
<br><br><br>
<footer>
  <p>
    <a id="ftr" href="#" class="fa fa-facebook"></a>
    <a id="ftr" href="#" class="fa fa-whatsapp"></a>
    <a id="ftr" href="#" class="fa fa-instagram"></a>
    <a id="ftr" href="#" class="fa fa-twitter"></a>
  </p>

  <br>
</footer>
<script>
$(document).ready(function(){

function openNav() {
document.getElementById("mySidenav").style.width = "70%";

document.body.style.backgroundColor = "rgba(0,0,0,0.4)";
}

function closeNav() {
document.getElementById("mySidenav").style.width = "0";
document.body.style.backgroundColor = "rgba(0,0,0,0)";
}


});
</script>

</body>
</html>
