<!DOCTYPE html>
{% load widget_tweaks %}
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8">
    <title>Online Shopping</title>
    <style media="screen">
      body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    min-height: 100vh;
}

.box {
    width: 500px;
    padding: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    text-align: center;
    transition: all 0.3s ease;
    margin-top: 100px;
    border-radius: 16px;
    box-shadow: 0 16px 48px rgba(26, 35, 126, 0.2);
    transform: translate(-50%, -50%);
}

.box input[type="text"],
.box input[type="password"] {
    border: 0;
    background: rgba(255, 255, 255, 0.9);
    display: block;
    margin: 20px auto;
    text-align: center;
    border: 2px solid #00acc1;
    padding: 12px 16px;
    width: 250px;
    outline: none;
    color: #212121;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.box h1 {
    color: #1a237e;
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 10px;
}

.text-muted {
    color: #666666 !important;
    font-size: 14px;
}

.box input[type="text"]:focus,
.box input[type="password"]:focus {
    width: 300px;
    border-color: #1a237e;
    box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
    background: rgba(255, 255, 255, 1);
}

.box input[type="submit"] {
    border: 0;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    display: block;
    margin: 20px auto;
    text-align: center;
    padding: 14px 40px;
    outline: none;
    color: white;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 16px rgba(26, 35, 126, 0.3);
}

.box input[type="submit"]:hover {
    background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 35, 126, 0.4);
}

.forgot {
    text-decoration: underline
}

ul.social-network {
    list-style: none;
    display: inline;
    margin-left: 0 !important;
    padding: 0
}

ul.social-network li {
    display: inline;
    margin: 0 5px
}

.social-network a.icoFacebook:hover {
    background-color: #3B5998
}

.social-network a.icoTwitter:hover {
    background-color: #33ccff
}

.social-network a.icoGoogle:hover {
    background-color: #BD3518
}

.social-network a.icoFacebook:hover i,
.social-network a.icoTwitter:hover i,
.social-network a.icoGoogle:hover i {
    color: #fff
}

a.socialIcon:hover,
.socialHoverClass {
    color: #44BCDD
}

.social-circle li a {
    display: inline-block;
    position: relative;
    margin: 0 auto 0 auto;
    border-radius: 50%;
    text-align: center;
    width: 50px;
    height: 50px;
    font-size: 20px
}

.social-circle li i {
    margin: 0;
    line-height: 50px;
    text-align: center
}

.social-circle li a:hover i,
.triggeredHover {
    transform: rotate(360deg);
    transition: all 0.2s
}

.social-circle i {
    color: #fff;
    transition: all 0.8s;
    transition: all 0.8s
}
    </style>
  </head>
  <body>
    {% include "ecom/navbar.html" %}
    <div class="container">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <form class="box" method="post">
                  {% csrf_token %}
                    <h1>Customer Login</h1>
                    <p class="text-muted"> Please enter your login and password!</p>
                               {% render_field form.username class="form-control" placeholder="Username" %}
                               {% render_field form.password class="form-control" placeholder="Password" %}
                     <input type="submit" name="" value="Login" >

                </form>
            </div>
        </div>
    </div>
</div>
<br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
{% include "ecom/footer.html" %}
  </body>
</html>
